{"name": "@form-builder/frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "tsc --noEmit", "preview": "vite preview"}, "dependencies": {"@form-builder/shared": "file:../shared", "@reduxjs/toolkit": "^2.0.1", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "react": "^19.1.1", "react-dom": "^19.1.1", "react-redux": "^9.0.4", "react-router-dom": "^6.20.1", "zod": "^3.22.4"}, "devDependencies": {"@eslint/js": "^9.32.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.16", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.4.32", "prettier": "^3.1.0", "tailwindcss": "^3.3.6", "typescript": "~5.8.3", "typescript-eslint": "^8.39.0", "vite": "^7.1.0"}}