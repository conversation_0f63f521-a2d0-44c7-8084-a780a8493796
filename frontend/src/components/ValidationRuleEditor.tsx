import { ValidationRule } from '@shared/types';

interface ValidationRuleEditorProps {
  validations: ValidationRule;
  onChange: (validations: ValidationRule) => void;
}

const ValidationRuleEditor = ({ validations, onChange }: ValidationRuleEditorProps) => {
  const handleChange = (key: keyof ValidationRule, value: boolean | number | undefined) => {
    onChange({
      ...validations,
      [key]: value,
    });
  };

  return (
    <div className="space-y-4">
      <h4 className="text-md font-medium text-gray-900">Validation Rules</h4>
      
      <div className="space-y-3">
        {/* Not Empty */}
        <div className="flex items-center">
          <input
            type="checkbox"
            id="notEmpty"
            checked={validations.notEmpty || false}
            onChange={(e) => handleChange('notEmpty', e.target.checked || undefined)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="notEmpty" className="ml-2 text-sm text-gray-700">
            Not empty (required)
          </label>
        </div>

        {/* Min Length */}
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="hasMinLength"
            checked={validations.minLength !== undefined}
            onChange={(e) => handleChange('minLength', e.target.checked ? 0 : undefined)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="hasMinLength" className="text-sm text-gray-700">
            Minimum length:
          </label>
          {validations.minLength !== undefined && (
            <input
              type="number"
              value={validations.minLength}
              onChange={(e) => handleChange('minLength', parseInt(e.target.value) || 0)}
              className="form-input w-20"
              min="0"
            />
          )}
        </div>

        {/* Max Length */}
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="hasMaxLength"
            checked={validations.maxLength !== undefined}
            onChange={(e) => handleChange('maxLength', e.target.checked ? 100 : undefined)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="hasMaxLength" className="text-sm text-gray-700">
            Maximum length:
          </label>
          {validations.maxLength !== undefined && (
            <input
              type="number"
              value={validations.maxLength}
              onChange={(e) => handleChange('maxLength', parseInt(e.target.value) || 100)}
              className="form-input w-20"
              min="0"
            />
          )}
        </div>

        {/* Email Format */}
        <div className="flex items-center">
          <input
            type="checkbox"
            id="email"
            checked={validations.email || false}
            onChange={(e) => handleChange('email', e.target.checked || undefined)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="email" className="ml-2 text-sm text-gray-700">
            Valid email format
          </label>
        </div>

        {/* Password Rule */}
        <div className="flex items-center">
          <input
            type="checkbox"
            id="passwordRule"
            checked={validations.passwordRule || false}
            onChange={(e) => handleChange('passwordRule', e.target.checked || undefined)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="passwordRule" className="ml-2 text-sm text-gray-700">
            Password rule (≥8 chars, ≥1 number)
          </label>
        </div>
      </div>

      {/* Validation Summary */}
      {Object.values(validations).some(v => v !== undefined) && (
        <div className="mt-4 p-3 bg-blue-50 rounded-md">
          <h5 className="text-sm font-medium text-blue-900 mb-2">Active Validations:</h5>
          <ul className="text-sm text-blue-800 space-y-1">
            {validations.notEmpty && <li>• Field is required</li>}
            {validations.minLength !== undefined && (
              <li>• Minimum {validations.minLength} characters</li>
            )}
            {validations.maxLength !== undefined && (
              <li>• Maximum {validations.maxLength} characters</li>
            )}
            {validations.email && <li>• Must be valid email format</li>}
            {validations.passwordRule && (
              <li>• Must be ≥8 characters with ≥1 number</li>
            )}
          </ul>
        </div>
      )}
    </div>
  );
};

export default ValidationRuleEditor;
