import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { FormField } from '@shared/types';

interface DraggableFieldItemProps {
  field: FormField;
  isSelected: boolean;
  onSelect: () => void;
  onRemove: () => void;
}

const FIELD_ICONS: Record<string, string> = {
  text: '📝',
  number: '🔢',
  textarea: '📄',
  select: '📋',
  radio: '🔘',
  checkbox: '☑️',
  date: '📅',
};

const DraggableFieldItem = ({ field, isSelected, onSelect, onRemove }: DraggableFieldItemProps) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: field.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (confirm(`Are you sure you want to remove "${field.label}"?`)) {
      onRemove();
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        relative bg-white border-2 rounded-lg p-4 cursor-pointer transition-all
        ${isSelected 
          ? 'border-blue-500 shadow-md' 
          : 'border-gray-200 hover:border-gray-300'
        }
        ${isDragging ? 'opacity-50' : ''}
      `}
      onClick={onSelect}
    >
      {/* Drag Handle */}
      <div
        {...attributes}
        {...listeners}
        className="absolute left-2 top-1/2 transform -translate-y-1/2 cursor-grab active:cursor-grabbing text-gray-400 hover:text-gray-600"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8h16M4 16h16" />
        </svg>
      </div>

      {/* Remove Button */}
      <button
        onClick={handleRemove}
        className="absolute right-2 top-2 text-gray-400 hover:text-red-600 transition-colors"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>

      {/* Field Content */}
      <div className="ml-6 mr-6">
        <div className="flex items-center space-x-2 mb-2">
          <span className="text-lg">{FIELD_ICONS[field.type] || '❓'}</span>
          <span className="font-medium text-gray-900">{field.label}</span>
          {field.required && <span className="text-red-500 text-sm">*</span>}
          {field.derived?.isDerived && (
            <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
              Derived
            </span>
          )}
        </div>

        <div className="text-sm text-gray-600 mb-2">
          Type: {field.type.charAt(0).toUpperCase() + field.type.slice(1)}
        </div>

        {/* Field Preview */}
        <div className="bg-gray-50 rounded p-2 text-sm">
          {field.type === 'text' && (
            <input
              type="text"
              placeholder={`Enter ${field.label.toLowerCase()}`}
              className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
              disabled
            />
          )}
          
          {field.type === 'number' && (
            <input
              type="number"
              placeholder={`Enter ${field.label.toLowerCase()}`}
              className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
              disabled
            />
          )}
          
          {field.type === 'textarea' && (
            <textarea
              placeholder={`Enter ${field.label.toLowerCase()}`}
              className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
              rows={2}
              disabled
            />
          )}
          
          {field.type === 'select' && (
            <select className="w-full px-2 py-1 border border-gray-300 rounded text-sm" disabled>
              <option>Select an option</option>
              {field.options?.map((option, index) => (
                <option key={index}>{option.label}</option>
              ))}
            </select>
          )}
          
          {field.type === 'radio' && (
            <div className="space-y-1">
              {field.options?.map((option, index) => (
                <label key={index} className="flex items-center text-sm">
                  <input type="radio" className="mr-2" disabled />
                  {option.label}
                </label>
              ))}
            </div>
          )}
          
          {field.type === 'checkbox' && (
            <div className="space-y-1">
              {field.options?.map((option, index) => (
                <label key={index} className="flex items-center text-sm">
                  <input type="checkbox" className="mr-2" disabled />
                  {option.label}
                </label>
              ))}
            </div>
          )}
          
          {field.type === 'date' && (
            <input
              type="date"
              className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
              disabled
            />
          )}
        </div>

        {/* Validation Summary */}
        {field.validations && Object.values(field.validations).some(v => v !== undefined) && (
          <div className="mt-2 text-xs text-blue-600">
            Validations: {Object.entries(field.validations)
              .filter(([_, value]) => value !== undefined)
              .map(([key]) => key)
              .join(', ')}
          </div>
        )}

        {/* Derived Field Info */}
        {field.derived?.isDerived && (
          <div className="mt-2 text-xs text-purple-600">
            Formula: {field.derived.formula || 'No formula set'}
          </div>
        )}
      </div>
    </div>
  );
};

export default DraggableFieldItem;
