import { FormField, FieldOption } from '@shared/types';

interface FormFieldRendererProps {
  field: FormField;
  value: unknown;
  onChange: (value: unknown) => void;
  error?: string[];
  disabled?: boolean;
}

const FormFieldRenderer = ({ 
  field, 
  value, 
  onChange, 
  error, 
  disabled = false 
}: FormFieldRendererProps) => {
  const hasError = error && error.length > 0;

  const renderInput = () => {
    const baseClasses = `form-input ${hasError ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`;
    
    switch (field.type) {
      case 'text':
        return (
          <input
            type="text"
            id={field.id}
            value={String(value || '')}
            onChange={(e) => onChange(e.target.value)}
            className={baseClasses}
            disabled={disabled}
            placeholder={`Enter ${field.label.toLowerCase()}`}
          />
        );

      case 'number':
        return (
          <input
            type="number"
            id={field.id}
            value={String(value || '')}
            onChange={(e) => onChange(e.target.value ? Number(e.target.value) : '')}
            className={baseClasses}
            disabled={disabled}
            placeholder={`Enter ${field.label.toLowerCase()}`}
          />
        );

      case 'textarea':
        return (
          <textarea
            id={field.id}
            value={String(value || '')}
            onChange={(e) => onChange(e.target.value)}
            className={`${baseClasses} min-h-[100px]`}
            disabled={disabled}
            placeholder={`Enter ${field.label.toLowerCase()}`}
            rows={4}
          />
        );

      case 'select':
        return (
          <select
            id={field.id}
            value={String(value || '')}
            onChange={(e) => onChange(e.target.value)}
            className={baseClasses}
            disabled={disabled}
          >
            <option value="">Select an option</option>
            {field.options?.map((option: FieldOption) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );

      case 'radio':
        return (
          <div className="space-y-2">
            {field.options?.map((option: FieldOption) => (
              <label key={option.value} className="flex items-center">
                <input
                  type="radio"
                  name={field.id}
                  value={option.value}
                  checked={value === option.value}
                  onChange={(e) => onChange(e.target.value)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  disabled={disabled}
                />
                <span className="ml-2 text-sm text-gray-700">{option.label}</span>
              </label>
            ))}
          </div>
        );

      case 'checkbox':
        const checkboxValues = Array.isArray(value) ? value : [];
        return (
          <div className="space-y-2">
            {field.options?.map((option: FieldOption) => (
              <label key={option.value} className="flex items-center">
                <input
                  type="checkbox"
                  value={option.value}
                  checked={checkboxValues.includes(option.value)}
                  onChange={(e) => {
                    const newValues = e.target.checked
                      ? [...checkboxValues, option.value]
                      : checkboxValues.filter(v => v !== option.value);
                    onChange(newValues);
                  }}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  disabled={disabled}
                />
                <span className="ml-2 text-sm text-gray-700">{option.label}</span>
              </label>
            ))}
          </div>
        );

      case 'date':
        return (
          <input
            type="date"
            id={field.id}
            value={String(value || '')}
            onChange={(e) => onChange(e.target.value)}
            className={baseClasses}
            disabled={disabled}
          />
        );

      default:
        return (
          <div className="text-red-500 text-sm">
            Unsupported field type: {field.type}
          </div>
        );
    }
  };

  return (
    <div className="mb-4">
      <label htmlFor={field.id} className="form-label">
        {field.label}
        {field.required && <span className="text-red-500 ml-1">*</span>}
        {field.derived?.isDerived && (
          <span className="ml-2 text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
            Derived
          </span>
        )}
      </label>
      
      {renderInput()}
      
      {hasError && (
        <div className="form-error">
          {error.map((err, index) => (
            <div key={index}>{err}</div>
          ))}
        </div>
      )}
      
      {field.derived?.isDerived && field.derived.formula && (
        <div className="text-xs text-gray-500 mt-1">
          Formula: {field.derived.formula}
        </div>
      )}
    </div>
  );
};

export default FormFieldRenderer;
