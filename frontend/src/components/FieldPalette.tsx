import { FieldType } from '@shared/types';

interface FieldPaletteProps {
  onAddField: (type: FieldType) => void;
}

const FIELD_TYPES: { type: FieldType; label: string; icon: string; description: string }[] = [
  {
    type: 'text',
    label: 'Text',
    icon: '📝',
    description: 'Single line text input',
  },
  {
    type: 'number',
    label: 'Number',
    icon: '🔢',
    description: 'Numeric input field',
  },
  {
    type: 'textarea',
    label: 'Textarea',
    icon: '📄',
    description: 'Multi-line text input',
  },
  {
    type: 'select',
    label: 'Select',
    icon: '📋',
    description: 'Dropdown selection',
  },
  {
    type: 'radio',
    label: 'Radio',
    icon: '🔘',
    description: 'Single choice from options',
  },
  {
    type: 'checkbox',
    label: 'Checkbox',
    icon: '☑️',
    description: 'Multiple choice selection',
  },
  {
    type: 'date',
    label: 'Date',
    icon: '📅',
    description: 'Date picker input',
  },
];

const FieldPalette = ({ onAddField }: FieldPaletteProps) => {
  return (
    <div className="p-4">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Field Types</h3>
      
      <div className="space-y-2">
        {FIELD_TYPES.map((fieldType) => (
          <button
            key={fieldType.type}
            onClick={() => onAddField(fieldType.type)}
            className="w-full text-left p-3 rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-colors group"
          >
            <div className="flex items-start space-x-3">
              <span className="text-xl">{fieldType.icon}</span>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-gray-900 group-hover:text-blue-900">
                  {fieldType.label}
                </div>
                <div className="text-xs text-gray-500 group-hover:text-blue-700 mt-1">
                  {fieldType.description}
                </div>
              </div>
            </div>
          </button>
        ))}
      </div>

      <div className="mt-6 p-3 bg-blue-50 rounded-lg">
        <h4 className="text-sm font-medium text-blue-900 mb-2">Tips</h4>
        <ul className="text-xs text-blue-800 space-y-1">
          <li>• Click a field type to add it to your form</li>
          <li>• Select fields to edit their properties</li>
          <li>• Drag fields to reorder them</li>
          <li>• Use derived fields for calculations</li>
        </ul>
      </div>
    </div>
  );
};

export default FieldPalette;
