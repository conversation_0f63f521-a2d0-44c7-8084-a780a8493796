import { useState, useEffect } from 'react';
import { FormField, FieldType, ValidationRule, FieldOption } from '@shared/types';
import ValidationRuleEditor from './ValidationRuleEditor';
import DerivedFieldEditor from './DerivedFieldEditor';

interface FieldEditorProps {
  field: FormField | null;
  allFields: FormField[];
  onUpdate: (field: FormField) => void;
  onClose: () => void;
}

const FIELD_TYPES: { value: FieldType; label: string }[] = [
  { value: 'text', label: 'Text' },
  { value: 'number', label: 'Number' },
  { value: 'textarea', label: 'Textarea' },
  { value: 'select', label: 'Select' },
  { value: 'radio', label: 'Radio' },
  { value: 'checkbox', label: 'Checkbox' },
  { value: 'date', label: 'Date' },
];

const FieldEditor = ({ field, allFields, onUpdate, onClose }: FieldEditorProps) => {
  const [editedField, setEditedField] = useState<FormField | null>(null);

  useEffect(() => {
    setEditedField(field);
  }, [field]);

  if (!editedField) {
    return (
      <div className="p-6 text-center text-gray-500">
        Select a field to edit its properties
      </div>
    );
  }

  const handleFieldChange = (updates: Partial<FormField>) => {
    const updatedField = { ...editedField, ...updates };
    setEditedField(updatedField);
    onUpdate(updatedField);
  };

  const handleValidationChange = (validations: ValidationRule) => {
    handleFieldChange({ validations });
  };

  const handleOptionsChange = (options: FieldOption[]) => {
    handleFieldChange({ options });
  };

  const addOption = () => {
    const currentOptions = editedField.options || [];
    const newOption: FieldOption = {
      label: `Option ${currentOptions.length + 1}`,
      value: `option_${currentOptions.length + 1}`,
    };
    handleOptionsChange([...currentOptions, newOption]);
  };

  const updateOption = (index: number, option: FieldOption) => {
    const currentOptions = editedField.options || [];
    const newOptions = [...currentOptions];
    newOptions[index] = option;
    handleOptionsChange(newOptions);
  };

  const removeOption = (index: number) => {
    const currentOptions = editedField.options || [];
    const newOptions = currentOptions.filter((_, i) => i !== index);
    handleOptionsChange(newOptions);
  };

  const needsOptions = ['select', 'radio', 'checkbox'].includes(editedField.type);

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">Field Properties</h3>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* Basic Properties */}
      <div className="space-y-4">
        <div>
          <label className="form-label">Field Type</label>
          <select
            value={editedField.type}
            onChange={(e) => handleFieldChange({ type: e.target.value as FieldType })}
            className="form-input"
          >
            {FIELD_TYPES.map(type => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="form-label">Label</label>
          <input
            type="text"
            value={editedField.label}
            onChange={(e) => handleFieldChange({ label: e.target.value })}
            className="form-input"
            placeholder="Enter field label"
          />
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="required"
            checked={editedField.required}
            onChange={(e) => handleFieldChange({ required: e.target.checked })}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="required" className="ml-2 text-sm text-gray-700">
            Required field
          </label>
        </div>

        <div>
          <label className="form-label">Default Value</label>
          <input
            type="text"
            value={String(editedField.defaultValue || '')}
            onChange={(e) => handleFieldChange({ defaultValue: e.target.value })}
            className="form-input"
            placeholder="Enter default value"
          />
        </div>
      </div>

      {/* Options for select/radio/checkbox */}
      {needsOptions && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-md font-medium text-gray-900">Options</h4>
            <button
              onClick={addOption}
              className="btn-secondary text-sm"
            >
              Add Option
            </button>
          </div>
          
          <div className="space-y-2">
            {(editedField.options || []).map((option, index) => (
              <div key={index} className="flex items-center space-x-2">
                <input
                  type="text"
                  value={option.label}
                  onChange={(e) => updateOption(index, { ...option, label: e.target.value })}
                  className="form-input flex-1"
                  placeholder="Option label"
                />
                <input
                  type="text"
                  value={option.value}
                  onChange={(e) => updateOption(index, { ...option, value: e.target.value })}
                  className="form-input flex-1"
                  placeholder="Option value"
                />
                <button
                  onClick={() => removeOption(index)}
                  className="text-red-600 hover:text-red-800"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Validation Rules */}
      <ValidationRuleEditor
        validations={editedField.validations || {}}
        onChange={handleValidationChange}
      />

      {/* Derived Field Configuration */}
      <DerivedFieldEditor
        field={editedField}
        allFields={allFields}
        onChange={(derived) => handleFieldChange({ derived })}
      />
    </div>
  );
};

export default FieldEditor;
