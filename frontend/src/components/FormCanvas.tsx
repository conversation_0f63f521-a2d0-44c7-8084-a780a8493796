import { 
  DndContext, 
  closestCenter, 
  KeyboardSensor, 
  PointerSensor, 
  useSensor, 
  useSensors,
  DragEndEvent
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { FormField } from '@shared/types';
import DraggableFieldItem from './DraggableFieldItem';

interface FormCanvasProps {
  fields: FormField[];
  selectedFieldId: string | null;
  onFieldSelect: (fieldId: string | null) => void;
  onFieldRemove: (fieldId: string) => void;
  onFieldReorder: (fromIndex: number, toIndex: number) => void;
}

const FormCanvas = ({
  fields,
  selectedFieldId,
  onFieldSelect,
  onFieldRemove,
  onFieldReorder,
}: FormCanvasProps) => {
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = fields.findIndex(field => field.id === active.id);
      const newIndex = fields.findIndex(field => field.id === over.id);
      
      onFieldReorder(oldIndex, newIndex);
    }
  };

  if (fields.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center">
          <div className="text-6xl text-gray-300 mb-4">📝</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Start Building Your Form
          </h3>
          <p className="text-gray-500 max-w-sm">
            Add fields from the palette on the left to start creating your form. 
            You can drag and drop to reorder them.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="max-w-2xl mx-auto">
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Form Preview</h2>
          <p className="text-sm text-gray-600">
            This is how your form will look. Click on fields to edit them.
          </p>
        </div>

        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext items={fields.map(f => f.id)} strategy={verticalListSortingStrategy}>
            <div className="space-y-4">
              {fields.map((field) => (
                <DraggableFieldItem
                  key={field.id}
                  field={field}
                  isSelected={selectedFieldId === field.id}
                  onSelect={() => onFieldSelect(field.id)}
                  onRemove={() => onFieldRemove(field.id)}
                />
              ))}
            </div>
          </SortableContext>
        </DndContext>

        <div className="mt-8 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900">Form Summary</h4>
              <p className="text-sm text-gray-600">
                {fields.length} field{fields.length !== 1 ? 's' : ''} • 
                {fields.filter(f => f.required).length} required • 
                {fields.filter(f => f.derived?.isDerived).length} derived
              </p>
            </div>
            <div className="text-sm text-gray-500">
              Click fields to edit • Drag to reorder
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FormCanvas;
