import { FormField, DerivedConfig } from '@shared/types';

interface DerivedFieldEditorProps {
  field: FormField;
  allFields: FormField[];
  onChange: (derived: DerivedConfig | undefined) => void;
}

const DerivedFieldEditor = ({ field, allFields, onChange }: DerivedFieldEditorProps) => {
  const derived = field.derived || { isDerived: false, parents: [] };
  
  // Get available parent fields (exclude current field and other derived fields)
  const availableParents = allFields.filter(f => 
    f.id !== field.id && 
    !f.derived?.isDerived &&
    ['number', 'text'].includes(f.type) // Only numeric-compatible fields
  );

  const handleDerivedToggle = (isDerived: boolean) => {
    if (isDerived) {
      onChange({
        isDerived: true,
        parents: [],
        formula: '',
      });
    } else {
      onChange(undefined);
    }
  };

  const handleParentToggle = (parentId: string, isSelected: boolean) => {
    const currentParents = derived.parents || [];
    const newParents = isSelected
      ? [...currentParents, parentId]
      : currentParents.filter(id => id !== parentId);
    
    onChange({
      ...derived,
      parents: newParents,
    });
  };

  const handleFormulaChange = (formula: string) => {
    onChange({
      ...derived,
      formula,
    });
  };

  return (
    <div className="space-y-4">
      <h4 className="text-md font-medium text-gray-900">Derived Field</h4>
      
      <div className="flex items-center">
        <input
          type="checkbox"
          id="isDerived"
          checked={derived.isDerived}
          onChange={(e) => handleDerivedToggle(e.target.checked)}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label htmlFor="isDerived" className="ml-2 text-sm text-gray-700">
          Make this a derived field
        </label>
      </div>

      {derived.isDerived && (
        <div className="space-y-4 pl-6 border-l-2 border-blue-200">
          {/* Parent Fields Selection */}
          <div>
            <label className="form-label">Parent Fields</label>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {availableParents.length === 0 ? (
                <p className="text-sm text-gray-500">
                  No available parent fields. Add some number or text fields first.
                </p>
              ) : (
                availableParents.map(parentField => (
                  <label key={parentField.id} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={(derived.parents || []).includes(parentField.id)}
                      onChange={(e) => handleParentToggle(parentField.id, e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      {parentField.label} ({parentField.type})
                    </span>
                  </label>
                ))
              )}
            </div>
          </div>

          {/* Formula Input */}
          <div>
            <label className="form-label">Formula</label>
            <textarea
              value={derived.formula || ''}
              onChange={(e) => handleFormulaChange(e.target.value)}
              className="form-input font-mono text-sm"
              rows={3}
              placeholder="Enter formula using $fieldId syntax, e.g., $field1 + $field2 * 2"
            />
            <div className="text-xs text-gray-500 mt-1">
              Use $fieldId to reference parent fields. Supported operations: +, -, *, /, ()
            </div>
          </div>

          {/* Selected Parents Reference */}
          {(derived.parents || []).length > 0 && (
            <div className="bg-gray-50 p-3 rounded-md">
              <h5 className="text-sm font-medium text-gray-900 mb-2">Available References:</h5>
              <div className="space-y-1">
                {derived.parents?.map(parentId => {
                  const parentField = allFields.find(f => f.id === parentId);
                  return parentField ? (
                    <div key={parentId} className="text-sm text-gray-700">
                      <code className="bg-blue-100 px-1 rounded">${parentId}</code> → {parentField.label}
                    </div>
                  ) : null;
                })}
              </div>
            </div>
          )}

          {/* Formula Examples */}
          <div className="bg-blue-50 p-3 rounded-md">
            <h5 className="text-sm font-medium text-blue-900 mb-2">Formula Examples:</h5>
            <ul className="text-sm text-blue-800 space-y-1">
              <li><code>$price * 1.08</code> - Add 8% tax</li>
              <li><code>$length * $width</code> - Calculate area</li>
              <li><code>($total - $discount) * $quantity</code> - Complex calculation</li>
              <li><code>2023 - $birthYear</code> - Calculate age</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default DerivedFieldEditor;
