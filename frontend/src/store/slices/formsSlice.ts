import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { 
  CreateFormRequest, 
  CreateFormResponse, 
  GetFormResponse, 
  ListFormsResponse 
} from '@shared/types';
import { apiClient } from '../../services/apiClient';

interface FormsState {
  forms: ListFormsResponse;
  currentForm: GetFormResponse | null;
  loading: boolean;
  error: string | null;
}

const initialState: FormsState = {
  forms: [],
  currentForm: null,
  loading: false,
  error: null,
};

// Async thunks
export const createForm = createAsyncThunk(
  'forms/create',
  async (formData: CreateFormRequest) => {
    const response = await apiClient.post<CreateFormResponse>('/forms', formData);
    return response;
  }
);

export const fetchForms = createAsyncThunk(
  'forms/fetchAll',
  async () => {
    const response = await apiClient.get<ListFormsResponse>('/forms');
    return response;
  }
);

export const fetchFormById = createAsyncThunk(
  'forms/fetchById',
  async (id: string) => {
    const response = await apiClient.get<GetFormResponse>(`/forms/${id}`);
    return response;
  }
);

const formsSlice = createSlice({
  name: 'forms',
  initialState,
  reducers: {
    clearCurrentForm: (state) => {
      state.currentForm = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Create form
    builder
      .addCase(createForm.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createForm.fulfilled, (state, action) => {
        state.loading = false;
        // Add the new form to the list (we only have basic info from create response)
        const newFormListItem = {
          id: action.payload.id,
          name: action.payload.name,
          createdAt: action.payload.createdAt,
        };
        state.forms.unshift(newFormListItem);
      })
      .addCase(createForm.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to create form';
      });

    // Fetch forms
    builder
      .addCase(fetchForms.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchForms.fulfilled, (state, action) => {
        state.loading = false;
        state.forms = action.payload;
      })
      .addCase(fetchForms.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch forms';
      });

    // Fetch form by ID
    builder
      .addCase(fetchFormById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchFormById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentForm = action.payload;
      })
      .addCase(fetchFormById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch form';
      });
  },
});

export const { clearCurrentForm, clearError } = formsSlice.actions;
export default formsSlice.reducer;
