import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { FormField } from '@shared/types';

interface PreviewState {
  formData: Record<string, unknown>;
  fieldErrors: Record<string, string[]>;
  derivedValues: Record<string, number>;
  isSubmitting: boolean;
}

const initialState: PreviewState = {
  formData: {},
  fieldErrors: {},
  derivedValues: {},
  isSubmitting: false,
};

const previewSlice = createSlice({
  name: 'preview',
  initialState,
  reducers: {
    setFieldValue: (state, action: PayloadAction<{ fieldId: string; value: unknown }>) => {
      const { fieldId, value } = action.payload;
      state.formData[fieldId] = value;
      
      // Clear field error when user starts typing
      if (state.fieldErrors[fieldId]) {
        delete state.fieldErrors[fieldId];
      }
    },
    setFieldError: (state, action: PayloadAction<{ fieldId: string; errors: string[] }>) => {
      const { fieldId, errors } = action.payload;
      if (errors.length > 0) {
        state.fieldErrors[fieldId] = errors;
      } else {
        delete state.fieldErrors[fieldId];
      }
    },
    setFieldErrors: (state, action: PayloadAction<Record<string, string[]>>) => {
      state.fieldErrors = action.payload;
    },
    setDerivedValue: (state, action: PayloadAction<{ fieldId: string; value: number }>) => {
      const { fieldId, value } = action.payload;
      state.derivedValues[fieldId] = value;
    },
    setDerivedValues: (state, action: PayloadAction<Record<string, number>>) => {
      state.derivedValues = action.payload;
    },
    clearFormData: (state) => {
      state.formData = {};
      state.fieldErrors = {};
      state.derivedValues = {};
    },
    setSubmitting: (state, action: PayloadAction<boolean>) => {
      state.isSubmitting = action.payload;
    },
    initializeFormData: (state, action: PayloadAction<FormField[]>) => {
      const fields = action.payload;
      const newFormData: Record<string, unknown> = {};
      
      // Initialize with default values
      fields.forEach(field => {
        if (field.defaultValue !== undefined) {
          newFormData[field.id] = field.defaultValue;
        } else {
          // Set appropriate default based on field type
          switch (field.type) {
            case 'checkbox':
              newFormData[field.id] = [];
              break;
            case 'number':
              newFormData[field.id] = '';
              break;
            default:
              newFormData[field.id] = '';
          }
        }
      });
      
      state.formData = newFormData;
      state.fieldErrors = {};
      state.derivedValues = {};
    },
  },
});

export const {
  setFieldValue,
  setFieldError,
  setFieldErrors,
  setDerivedValue,
  setDerivedValues,
  clearFormData,
  setSubmitting,
  initializeFormData,
} = previewSlice.actions;

export default previewSlice.reducer;
