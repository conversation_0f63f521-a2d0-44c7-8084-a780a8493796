import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { FormField } from '@shared/types';

interface BuilderState {
  formName: string;
  fields: FormField[];
  selectedFieldId: string | null;
  isDirty: boolean;
}

const initialState: BuilderState = {
  formName: '',
  fields: [],
  selectedFieldId: null,
  isDirty: false,
};

const builderSlice = createSlice({
  name: 'builder',
  initialState,
  reducers: {
    setFormName: (state, action: PayloadAction<string>) => {
      state.formName = action.payload;
      state.isDirty = true;
    },
    addField: (state, action: PayloadAction<FormField>) => {
      state.fields.push(action.payload);
      state.selectedFieldId = action.payload.id;
      state.isDirty = true;
    },
    updateField: (state, action: PayloadAction<FormField>) => {
      const index = state.fields.findIndex(f => f.id === action.payload.id);
      if (index !== -1) {
        state.fields[index] = action.payload;
        state.isDirty = true;
      }
    },
    removeField: (state, action: PayloadAction<string>) => {
      state.fields = state.fields.filter(f => f.id !== action.payload);
      if (state.selectedFieldId === action.payload) {
        state.selectedFieldId = null;
      }
      state.isDirty = true;
    },
    reorderFields: (state, action: PayloadAction<{ fromIndex: number; toIndex: number }>) => {
      const { fromIndex, toIndex } = action.payload;
      const [movedField] = state.fields.splice(fromIndex, 1);
      state.fields.splice(toIndex, 0, movedField);
      state.isDirty = true;
    },
    selectField: (state, action: PayloadAction<string | null>) => {
      state.selectedFieldId = action.payload;
    },
    loadForm: (state, action: PayloadAction<{ name: string; fields: FormField[] }>) => {
      state.formName = action.payload.name;
      state.fields = action.payload.fields;
      state.selectedFieldId = null;
      state.isDirty = false;
    },
    resetForm: (state) => {
      state.formName = '';
      state.fields = [];
      state.selectedFieldId = null;
      state.isDirty = false;
    },
    markClean: (state) => {
      state.isDirty = false;
    },
  },
});

export const {
  setFormName,
  addField,
  updateField,
  removeField,
  reorderFields,
  selectField,
  loadForm,
  resetForm,
  markClean,
} = builderSlice.actions;

export default builderSlice.reducer;
