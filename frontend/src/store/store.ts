import { configureStore } from '@reduxjs/toolkit';
import builderReducer from './slices/builderSlice';
import formsReducer from './slices/formsSlice';
import previewReducer from './slices/previewSlice';

export const store = configureStore({
  reducer: {
    builder: builderReducer,
    forms: formsReducer,
    preview: previewReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
