import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Provider } from 'react-redux';
import { store } from './store/store';
import Layout from './components/Layout';
import CreateFormPage from './pages/CreateFormPage';
import PreviewFormPage from './pages/PreviewFormPage';
import MyFormsPage from './pages/MyFormsPage';

function App() {
  return (
    <Provider store={store}>
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<MyFormsPage />} />
            <Route path="/create" element={<CreateFormPage />} />
            <Route path="/preview" element={<PreviewFormPage />} />
            <Route path="/myforms" element={<MyFormsPage />} />
          </Routes>
        </Layout>
      </Router>
    </Provider>
  );
}

export default App;
