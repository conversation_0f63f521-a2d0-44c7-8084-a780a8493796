import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchFormById } from '../store/slices/formsSlice';
import {
  initializeFormData,
  setFieldValue,
  setFieldErrors,
  setDerivedValues,
  clearFormData
} from '../store/slices/previewSlice';
import { validateFieldValue } from '@shared/validation';
import { DerivedEvaluator } from '../utils/derivedEvaluator';
import FormFieldRenderer from '../components/FormFieldRenderer';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';

const PreviewFormPage = () => {
  const dispatch = useAppDispatch();
  const [searchParams] = useSearchParams();
  const formId = searchParams.get('id');

  const { currentForm, loading, error } = useAppSelector(state => state.forms);
  const { fields: builderFields } = useAppSelector(state => state.builder);
  const { formData, fieldErrors, derivedValues } = useAppSelector(state => state.preview);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  // Use form from URL parameter or current builder form
  const form = formId ? currentForm : {
    name: 'Form Preview',
    fields: builderFields,
    id: 'preview',
    createdAt: new Date().toISOString()
  };

  useEffect(() => {
    if (formId) {
      dispatch(fetchFormById(formId));
    }
  }, [dispatch, formId]);

  useEffect(() => {
    if (form?.fields) {
      dispatch(initializeFormData(form.fields));
    }

    return () => {
      dispatch(clearFormData());
    };
  }, [dispatch, form?.fields]);

  // Compute derived values when form data changes
  useEffect(() => {
    if (!form?.fields) return;

    const newDerivedValues: Record<string, number> = {};

    form.fields.forEach(field => {
      if (field.derived?.isDerived && field.derived.formula) {
        const result = DerivedEvaluator.evaluate(field.derived.formula, formData);
        if (result !== null) {
          newDerivedValues[field.id] = result;
        }
      }
    });

    dispatch(setDerivedValues(newDerivedValues));
  }, [dispatch, form?.fields, formData]);

  const handleFieldChange = (fieldId: string, value: unknown) => {
    dispatch(setFieldValue({ fieldId, value }));

    // Validate field on change
    const field = form?.fields.find(f => f.id === fieldId);
    if (field) {
      const validation = validateFieldValue(value, field.validations, field.type);
      dispatch(setFieldErrors({ [fieldId]: validation.errors }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!form?.fields) return;

    setIsSubmitting(true);

    // Validate all fields
    const errors: Record<string, string[]> = {};
    let hasErrors = false;

    form.fields.forEach(field => {
      if (field.derived?.isDerived) return; // Skip derived fields

      const value = formData[field.id];
      const validation = validateFieldValue(value, field.validations, field.type);

      if (!validation.isValid) {
        errors[field.id] = validation.errors;
        hasErrors = true;
      }
    });

    dispatch(setFieldErrors(errors));

    if (!hasErrors) {
      // Simulate form submission
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSubmitSuccess(true);
      console.log('Form submitted:', { formData, derivedValues });
    }

    setIsSubmitting(false);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading form...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <Card className="max-w-md">
          <div className="text-center">
            <div className="text-red-500 text-4xl mb-4">⚠️</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Form</h3>
            <p className="text-gray-600">{error}</p>
          </div>
        </Card>
      </div>
    );
  }

  if (!form?.fields || form.fields.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <Card className="max-w-md">
          <div className="text-center">
            <div className="text-gray-400 text-4xl mb-4">📝</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Form to Preview</h3>
            <p className="text-gray-600">
              {formId
                ? 'This form could not be found or has no fields.'
                : 'Create a form first to see the preview.'
              }
            </p>
          </div>
        </Card>
      </div>
    );
  }

  if (submitSuccess) {
    return (
      <div className="max-w-2xl mx-auto">
        <Card>
          <div className="text-center">
            <div className="text-green-500 text-4xl mb-4">✅</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Form Submitted Successfully!</h3>
            <p className="text-gray-600 mb-4">Thank you for your submission.</p>
            <Button onClick={() => setSubmitSuccess(false)}>
              Submit Another Response
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      <Card>
        <form onSubmit={handleSubmit}>
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">{form.name}</h1>
            <p className="text-gray-600">
              Fill out this form. Fields marked with * are required.
            </p>
          </div>

          <div className="space-y-6">
            {form.fields.map(field => {
              const value = field.derived?.isDerived
                ? derivedValues[field.id] ?? 0
                : formData[field.id];

              return (
                <FormFieldRenderer
                  key={field.id}
                  field={field}
                  value={value}
                  onChange={(newValue) => handleFieldChange(field.id, newValue)}
                  error={fieldErrors[field.id]}
                  disabled={field.derived?.isDerived}
                />
              );
            })}
          </div>

          <div className="mt-8 flex justify-end">
            <Button
              type="submit"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              Submit Form
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
};

export default PreviewFormPage;
