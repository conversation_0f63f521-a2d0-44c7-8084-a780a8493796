import { useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchForms } from '../store/slices/formsSlice';
import { resetForm } from '../store/slices/builderSlice';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';

const MyFormsPage = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { forms, loading, error } = useAppSelector(state => state.forms);

  useEffect(() => {
    dispatch(fetchForms());
  }, [dispatch]);

  const handleCreateNew = () => {
    dispatch(resetForm());
    navigate('/create');
  };

  const handlePreviewForm = (formId: string) => {
    navigate(`/preview?id=${formId}`);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading forms...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <Card className="max-w-md">
          <div className="text-center">
            <div className="text-red-500 text-4xl mb-4">⚠️</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Forms</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={() => dispatch(fetchForms())}>
              Try Again
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">My Forms</h1>
          <p className="text-gray-600 mt-1">
            Manage and preview your created forms
          </p>
        </div>
        <Button onClick={handleCreateNew}>
          Create New Form
        </Button>
      </div>

      {/* Forms Grid */}
      {forms.length === 0 ? (
        <Card className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">📝</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No Forms Yet
          </h3>
          <p className="text-gray-600 mb-6 max-w-sm mx-auto">
            You haven't created any forms yet. Start building your first form to collect responses.
          </p>
          <Button onClick={handleCreateNew}>
            Create Your First Form
          </Button>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {forms.map((form) => (
            <Card key={form.id} className="hover:shadow-lg transition-shadow">
              <div className="flex flex-col h-full">
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="text-lg font-semibold text-gray-900 truncate">
                      {form.name}
                    </h3>
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => handlePreviewForm(form.id)}
                        className="text-gray-400 hover:text-blue-600 transition-colors"
                        title="Preview form"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      </button>
                    </div>
                  </div>

                  <div className="text-sm text-gray-600 mb-4">
                    Created {formatDate(form.createdAt)}
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <span className="flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      Form
                    </span>
                  </div>

                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => handlePreviewForm(form.id)}
                  >
                    Preview
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Quick Actions */}
      {forms.length > 0 && (
        <div className="mt-12 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-medium text-blue-900 mb-2">Quick Actions</h3>
          <div className="flex flex-wrap gap-3">
            <Link to="/create" className="btn-secondary text-sm">
              Create New Form
            </Link>
            <Link to="/preview" className="btn-secondary text-sm">
              Preview Current Form
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

export default MyFormsPage;
