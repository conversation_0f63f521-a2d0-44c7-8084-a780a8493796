import { useState } from 'react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import {
  setFormName,
  addField,
  updateField,
  selectField,
  removeField,
  reorderFields,
  resetForm,
  markClean
} from '../store/slices/builderSlice';
import { createForm } from '../store/slices/formsSlice';
import { FormField, FieldType } from '@shared/types';
import FieldPalette from '../components/FieldPalette';
import FormCanvas from '../components/FormCanvas';
import FieldEditor from '../components/FieldEditor';
import Button from '../components/ui/Button';
import Modal from '../components/ui/Modal';
import { useNavigate } from 'react-router-dom';

const CreateFormPage = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { formName, fields, selectedFieldId, isDirty } = useAppSelector(state => state.builder);
  const { loading } = useAppSelector(state => state.forms);

  const [showSaveModal, setShowSaveModal] = useState(false);
  const [saveFormName, setSaveFormName] = useState('');

  const selectedField = fields.find(f => f.id === selectedFieldId) || null;

  const handleAddField = (type: FieldType) => {
    const newField: FormField = {
      id: `field_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      label: `${type.charAt(0).toUpperCase() + type.slice(1)} Field`,
      required: false,
    };

    // Add default options for select/radio/checkbox fields
    if (['select', 'radio', 'checkbox'].includes(type)) {
      newField.options = [
        { label: 'Option 1', value: 'option1' },
        { label: 'Option 2', value: 'option2' },
      ];
    }

    dispatch(addField(newField));
  };

  const handleFieldUpdate = (updatedField: FormField) => {
    dispatch(updateField(updatedField));
  };

  const handleFieldSelect = (fieldId: string | null) => {
    dispatch(selectField(fieldId));
  };

  const handleFieldRemove = (fieldId: string) => {
    dispatch(removeField(fieldId));
  };

  const handleFieldReorder = (fromIndex: number, toIndex: number) => {
    dispatch(reorderFields({ fromIndex, toIndex }));
  };

  const handleSave = () => {
    setSaveFormName(formName || '');
    setShowSaveModal(true);
  };

  const handleSaveConfirm = async () => {
    if (!saveFormName.trim()) return;

    try {
      await dispatch(createForm({
        name: saveFormName,
        fields,
      })).unwrap();

      dispatch(markClean());
      setShowSaveModal(false);
      navigate('/myforms');
    } catch (error) {
      console.error('Failed to save form:', error);
    }
  };

  const handleNewForm = () => {
    if (isDirty) {
      if (confirm('You have unsaved changes. Are you sure you want to start a new form?')) {
        dispatch(resetForm());
      }
    } else {
      dispatch(resetForm());
    }
  };

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold text-gray-900">Form Builder</h1>
            <input
              type="text"
              value={formName}
              onChange={(e) => dispatch(setFormName(e.target.value))}
              placeholder="Enter form name..."
              className="form-input w-64"
            />
            {isDirty && (
              <span className="text-sm text-orange-600">• Unsaved changes</span>
            )}
          </div>

          <div className="flex items-center space-x-3">
            <Button variant="secondary" onClick={handleNewForm}>
              New Form
            </Button>
            <Button
              onClick={handleSave}
              disabled={!formName.trim() || fields.length === 0}
            >
              Save Form
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Panel - Field Palette */}
        <div className="w-64 bg-gray-50 border-r border-gray-200 overflow-y-auto">
          <FieldPalette onAddField={handleAddField} />
        </div>

        {/* Center - Form Canvas */}
        <div className="flex-1 overflow-y-auto">
          <FormCanvas
            fields={fields}
            selectedFieldId={selectedFieldId}
            onFieldSelect={handleFieldSelect}
            onFieldRemove={handleFieldRemove}
            onFieldReorder={handleFieldReorder}
          />
        </div>

        {/* Right Panel - Field Editor */}
        <div className="w-80 bg-white border-l border-gray-200 overflow-y-auto">
          <FieldEditor
            field={selectedField}
            allFields={fields}
            onUpdate={handleFieldUpdate}
            onClose={() => handleFieldSelect(null)}
          />
        </div>
      </div>

      {/* Save Modal */}
      <Modal
        isOpen={showSaveModal}
        onClose={() => setShowSaveModal(false)}
        title="Save Form"
      >
        <div className="space-y-4">
          <div>
            <label className="form-label">Form Name</label>
            <input
              type="text"
              value={saveFormName}
              onChange={(e) => setSaveFormName(e.target.value)}
              className="form-input"
              placeholder="Enter form name"
              autoFocus
            />
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              variant="secondary"
              onClick={() => setShowSaveModal(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSaveConfirm}
              loading={loading}
              disabled={!saveFormName.trim()}
            >
              Save Form
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default CreateFormPage;
