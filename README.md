# Form Builder - Full Stack Application

A dynamic form builder application built with React, TypeScript, Node.js, and Express following SOLID principles.

## Architecture

This is a monorepo containing:
- `/backend` - Node.js/Express API with TypeScript
- `/frontend` - React/Vite application with TypeScript
- `/shared` - Shared types and Zod schemas

## Tech Stack

### Frontend
- React.js with TypeScript
- Redux Toolkit for state management
- React Router for routing
- Tailwind CSS for styling
- Z<PERSON> for validation
- Vite for build tooling
- @dnd-kit for drag and drop

### Backend
- Node.js with Express.js
- TypeScript with strict mode
- Class-based architecture following SOLID principles
- Supabase (PostgreSQL) for database
- <PERSON><PERSON> for logging
- Zod for validation
- Jest for testing

## Features

- **Form Builder** (`/create`) - Build forms by adding and configuring fields
- **Form Preview** (`/preview`) - Interactive form preview with validation
- **My Forms** (`/myforms`) - List and manage saved forms

### Field Types
- Text, Number, Textarea
- Select, Radio, Checkbox
- Date

### Field Configuration
- Label and required toggle
- Default values
- Validation rules (not empty, min/max length, email, password)
- Derived fields with formulas

## Setup Instructions

### Prerequisites
- Node.js 18+ and npm
- Supabase account and project

### Database Setup

1. Create a Supabase project
2. Run this SQL in your Supabase SQL editor:

```sql
create table if not exists forms (
  id uuid primary key default gen_random_uuid(),
  name text not null,
  schema jsonb not null,
  created_at timestamptz not null default now()
);
create index if not exists forms_created_at_idx on forms (created_at desc);
```

### Environment Variables

1. Backend - Copy `backend/.env.example` to `backend/.env`:
```
PORT=4000
SUPABASE_URL=your_supabase_url_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

2. Frontend - Copy `frontend/.env.example` to `frontend/.env`:
```
VITE_API_BASE_URL=http://localhost:4000/api
```

### Installation & Development

```bash
# Install dependencies for all packages
npm install
cd shared && npm install
cd ../backend && npm install
cd ../frontend && npm install
cd ..

# Build shared package
npm run build:shared

# Run development servers (both frontend and backend)
npm run dev

# Or run individually:
npm run dev:backend  # Backend on http://localhost:4000
npm run dev:frontend # Frontend on http://localhost:5173
```

### Production Build

```bash
npm run build
```

### Testing

```bash
npm run test        # Run backend tests
npm run lint        # Lint all packages
npm run typecheck   # Type check all packages
```

## Design Decisions

### SOLID Principles Implementation
- **Single Responsibility**: Each class has one reason to change
- **Open/Closed**: Services are open for extension, closed for modification
- **Liskov Substitution**: Interfaces can be substituted without breaking functionality
- **Interface Segregation**: Small, focused interfaces
- **Dependency Inversion**: High-level modules don't depend on low-level modules

### Expression Evaluator
- Safe evaluation of derived field formulas
- Whitelist-based token validation
- Support for basic arithmetic operations
- Field references using `$fieldId` syntax

### Validation Strategy
- Zod schemas shared between frontend and backend
- Defense in depth with validation at multiple layers
- Consistent error handling and reporting

## API Endpoints

- `POST /api/forms` - Create a new form
- `GET /api/forms` - List all forms
- `GET /api/forms/:id` - Get a specific form

## Development

The application follows modern development practices:
- Strict TypeScript configuration
- ESLint and Prettier for code quality
- Husky for pre-commit hooks
- Comprehensive error handling
- Structured logging
- Repository pattern for data access
