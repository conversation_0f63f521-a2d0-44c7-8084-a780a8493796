import { createClient } from '@supabase/supabase-js';
import { env } from './env';
import { logger } from './logger';

export const supabase = createClient(
  env.SUPABASE_URL,
  env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

// Test connection
supabase
  .from('forms')
  .select('count', { count: 'exact', head: true })
  .then(({ error }) => {
    if (error) {
      logger.error('Failed to connect to Supabase:', error);
    } else {
      logger.info('✅ Connected to Supabase');
    }
  });
