import { FormController } from './modules/forms/controllers/FormController';
import { FormService } from './modules/forms/services/FormService';
import { SupabaseFormRepository } from './modules/forms/repositories/SupabaseFormRepository';

/**
 * Composition root for dependency injection
 * This is where we wire up all our dependencies
 */
export class CompositionRoot {
  private static _formController: FormController;

  static get formController(): FormController {
    if (!this._formController) {
      const formRepository = new SupabaseFormRepository();
      const formService = new FormService(formRepository);
      this._formController = new FormController(formService);
    }
    return this._formController;
  }

  // Reset for testing
  static reset(): void {
    this._formController = undefined as any;
  }
}
