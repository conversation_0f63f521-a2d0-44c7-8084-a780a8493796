import { IFormService } from './IFormService';
import { IFormRepository } from '../repositories/IFormRepository';
import { 
  CreateFormRequest, 
  CreateFormResponse, 
  GetFormResponse, 
  ListFormsResponse,
  CreateFormRequestSchema,
  FormFieldSchema
} from '@form-builder/shared';
import { Result, isFailure } from '../../../core/utils/Result';
import { ValidationError, BadRequestError } from '../../../core/errors/AppError';
import { logger } from '../../../config/logger';

export class FormService implements IFormService {
  constructor(private readonly formRepository: IFormRepository) {}

  async createForm(formData: CreateFormRequest): Promise<Result<CreateFormResponse>> {
    try {
      // Validate input with Zod
      const validationResult = CreateFormRequestSchema.safeParse(formData);
      if (!validationResult.success) {
        return Result.failure(new ValidationError('Invalid form data', validationResult.error.errors));
      }

      // Additional business logic validation
      const businessValidation = this.validateFormBusinessRules(formData);
      if (isFailure(businessValidation)) {
        return businessValidation;
      }

      // Create form
      const createResult = await this.formRepository.create(formData);
      if (isFailure(createResult)) {
        return createResult;
      }

      const form = createResult.data;
      const response: CreateFormResponse = {
        id: form.id,
        name: form.name,
        createdAt: form.createdAt.toISOString(),
      };

      logger.info(`Form created successfully: ${form.id}`);
      return Result.success(response);
    } catch (error) {
      logger.error('Error in createForm service:', error);
      return Result.failure(error as Error);
    }
  }

  async getFormById(id: string): Promise<Result<GetFormResponse | null>> {
    try {
      if (!id || typeof id !== 'string') {
        return Result.failure(new BadRequestError('Invalid form ID'));
      }

      const result = await this.formRepository.findById(id);
      if (isFailure(result)) {
        return result;
      }

      if (!result.data) {
        return Result.success(null);
      }

      const response = result.data.toResponse();
      return Result.success(response);
    } catch (error) {
      logger.error('Error in getFormById service:', error);
      return Result.failure(error as Error);
    }
  }

  async getAllForms(): Promise<Result<ListFormsResponse>> {
    try {
      const result = await this.formRepository.findAll();
      if (isFailure(result)) {
        return result;
      }

      const response = result.data.map(form => form.toListItem());
      return Result.success(response);
    } catch (error) {
      logger.error('Error in getAllForms service:', error);
      return Result.failure(error as Error);
    }
  }

  private validateFormBusinessRules(formData: CreateFormRequest): Result<void> {
    // Check for duplicate field IDs
    const fieldIds = formData.fields.map(field => field.id);
    const uniqueIds = new Set(fieldIds);
    if (fieldIds.length !== uniqueIds.size) {
      return Result.failure(new ValidationError('Duplicate field IDs found'));
    }

    // Validate each field
    for (const field of formData.fields) {
      const fieldValidation = FormFieldSchema.safeParse(field);
      if (!fieldValidation.success) {
        return Result.failure(new ValidationError(`Invalid field: ${field.id}`, fieldValidation.error.errors));
      }

      // Validate field-specific rules
      if ((field.type === 'select' || field.type === 'radio' || field.type === 'checkbox') && !field.options?.length) {
        return Result.failure(new ValidationError(`Field ${field.id} of type ${field.type} must have options`));
      }

      // Validate derived field rules
      if (field.derived?.isDerived) {
        if (!field.derived.parents?.length) {
          return Result.failure(new ValidationError(`Derived field ${field.id} must have parent fields`));
        }
        
        // Check that parent fields exist
        for (const parentId of field.derived.parents) {
          if (!fieldIds.includes(parentId)) {
            return Result.failure(new ValidationError(`Parent field ${parentId} not found for derived field ${field.id}`));
          }
        }
      }
    }

    return Result.success(undefined);
  }
}
