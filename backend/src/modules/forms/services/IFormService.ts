import { 
  CreateFormRequest, 
  CreateFormResponse, 
  GetFormResponse, 
  ListFormsResponse 
} from '@form-builder/shared';
import { Result } from '../../../core/utils/Result';

export interface IFormService {
  createForm(formData: CreateFormRequest): Promise<Result<CreateFormResponse>>;
  getFormById(id: string): Promise<Result<GetFormResponse | null>>;
  getAllForms(): Promise<Result<ListFormsResponse>>;
}
