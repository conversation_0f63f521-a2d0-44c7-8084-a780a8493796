import { IFormRepository } from './IFormRepository';
import { Form } from '../entities/Form';
import { CreateFormRequest } from '@form-builder/shared';
import { Result } from '../../../core/utils/Result';
import { supabase } from '../../../config/supabase';
import { logger } from '../../../config/logger';

export class SupabaseFormRepository implements IFormRepository {
  async create(formData: CreateFormRequest): Promise<Result<Form>> {
    try {
      const { data, error } = await supabase
        .from('forms')
        .insert({
          name: formData.name,
          schema: formData.fields,
        })
        .select()
        .single();

      if (error) {
        logger.error('Failed to create form:', error);
        return Result.failure(new Error(`Failed to create form: ${error.message}`));
      }

      const form = Form.fromDatabase(data);
      return Result.success(form);
    } catch (error) {
      logger.error('Unexpected error creating form:', error);
      return Result.failure(error as Error);
    }
  }

  async findById(id: string): Promise<Result<Form | null>> {
    try {
      const { data, error } = await supabase
        .from('forms')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned
          return Result.success(null);
        }
        logger.error('Failed to find form by id:', error);
        return Result.failure(new Error(`Failed to find form: ${error.message}`));
      }

      const form = Form.fromDatabase(data);
      return Result.success(form);
    } catch (error) {
      logger.error('Unexpected error finding form:', error);
      return Result.failure(error as Error);
    }
  }

  async findAll(): Promise<Result<Form[]>> {
    try {
      const { data, error } = await supabase
        .from('forms')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        logger.error('Failed to fetch forms:', error);
        return Result.failure(new Error(`Failed to fetch forms: ${error.message}`));
      }

      const forms = data.map(row => Form.fromDatabase(row));
      return Result.success(forms);
    } catch (error) {
      logger.error('Unexpected error fetching forms:', error);
      return Result.failure(error as Error);
    }
  }
}
