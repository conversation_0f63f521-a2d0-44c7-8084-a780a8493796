import { FormSchema } from '@form-builder/shared';

export class Form {
  constructor(
    public readonly id: string,
    public readonly name: string,
    public readonly schema: FormSchema['fields'],
    public readonly createdAt: Date
  ) {}

  static fromDatabase(row: {
    id: string;
    name: string;
    schema: unknown;
    created_at: string;
  }): Form {
    return new Form(
      row.id,
      row.name,
      row.schema as FormSchema['fields'],
      new Date(row.created_at)
    );
  }

  toResponse(): FormSchema {
    return {
      id: this.id,
      name: this.name,
      fields: this.schema,
      createdAt: this.createdAt.toISOString(),
    };
  }

  toListItem() {
    return {
      id: this.id,
      name: this.name,
      createdAt: this.createdAt.toISOString(),
    };
  }
}
