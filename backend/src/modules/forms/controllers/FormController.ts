import { Request, Response, NextFunction } from 'express';
import { IFormService } from '../services/IFormService';
import { CreateFormRequest } from '@form-builder/shared';
import { isFailure } from '../../../core/utils/Result';
import { NotFoundError } from '../../../core/errors/AppError';

export class FormController {
  constructor(private readonly formService: IFormService) {}

  createForm = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const formData: CreateFormRequest = req.body;
      
      const result = await this.formService.createForm(formData);
      
      if (isFailure(result)) {
        return next(result.error);
      }

      res.status(201).json(result.data);
    } catch (error) {
      next(error);
    }
  };

  getForm = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      
      const result = await this.formService.getFormById(id);
      
      if (isFailure(result)) {
        return next(result.error);
      }

      if (!result.data) {
        return next(new NotFoundError('Form not found'));
      }

      res.json(result.data);
    } catch (error) {
      next(error);
    }
  };

  getAllForms = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const result = await this.formService.getAllForms();
      
      if (isFailure(result)) {
        return next(result.error);
      }

      res.json(result.data);
    } catch (error) {
      next(error);
    }
  };
}
