import { Router } from 'express';
import { CompositionRoot } from '../../compositionRoot';

const router = Router();

// Get controller from composition root
const formController = CompositionRoot.formController;

// Form routes
router.post('/forms', formController.createForm);
router.get('/forms', formController.getAllForms);
router.get('/forms/:id', formController.getForm);

export { router as formRoutes };
