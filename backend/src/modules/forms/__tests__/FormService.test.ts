import { FormService } from '../services/FormService';
import { IFormRepository } from '../repositories/IFormRepository';
import { Form } from '../entities/Form';
import { CreateFormRequest } from '@form-builder/shared';
import { Result } from '../../../core/utils/Result';
import { ValidationError } from '../../../core/errors/AppError';

// Mock repository
class MockFormRepository implements IFormRepository {
  private forms: Form[] = [];
  private nextId = 1;

  async create(formData: CreateFormRequest): Promise<Result<Form>> {
    const form = new Form(
      `form-${this.nextId++}`,
      formData.name,
      formData.fields,
      new Date()
    );
    this.forms.push(form);
    return Result.success(form);
  }

  async findById(id: string): Promise<Result<Form | null>> {
    const form = this.forms.find(f => f.id === id);
    return Result.success(form || null);
  }

  async findAll(): Promise<Result<Form[]>> {
    return Result.success([...this.forms]);
  }

  reset() {
    this.forms = [];
    this.nextId = 1;
  }
}

describe('FormService', () => {
  let formService: FormService;
  let mockRepository: MockFormRepository;

  beforeEach(() => {
    mockRepository = new MockFormRepository();
    formService = new FormService(mockRepository);
  });

  describe('createForm', () => {
    it('should create a valid form', async () => {
      const formData: CreateFormRequest = {
        name: 'Test Form',
        fields: [
          {
            id: 'field1',
            type: 'text',
            label: 'Name',
            required: true,
          },
        ],
      };

      const result = await formService.createForm(formData);

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.name).toBe('Test Form');
        expect(result.data.id).toBeDefined();
        expect(result.data.createdAt).toBeDefined();
      }
    });

    it('should reject form with duplicate field IDs', async () => {
      const formData: CreateFormRequest = {
        name: 'Test Form',
        fields: [
          {
            id: 'field1',
            type: 'text',
            label: 'Name',
            required: true,
          },
          {
            id: 'field1', // Duplicate ID
            type: 'email',
            label: 'Email',
            required: true,
          },
        ],
      };

      const result = await formService.createForm(formData);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error).toBeInstanceOf(ValidationError);
        expect(result.error.message).toContain('Duplicate field IDs');
      }
    });

    it('should reject select field without options', async () => {
      const formData: CreateFormRequest = {
        name: 'Test Form',
        fields: [
          {
            id: 'field1',
            type: 'select',
            label: 'Choose Option',
            required: true,
            // Missing options
          },
        ],
      };

      const result = await formService.createForm(formData);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error).toBeInstanceOf(ValidationError);
        expect(result.error.message).toContain('must have options');
      }
    });

    it('should reject derived field with non-existent parent', async () => {
      const formData: CreateFormRequest = {
        name: 'Test Form',
        fields: [
          {
            id: 'field1',
            type: 'number',
            label: 'Result',
            required: false,
            derived: {
              isDerived: true,
              parents: ['nonExistentField'],
              formula: '$nonExistentField * 2',
            },
          },
        ],
      };

      const result = await formService.createForm(formData);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error).toBeInstanceOf(ValidationError);
        expect(result.error.message).toContain('Parent field nonExistentField not found');
      }
    });

    it('should accept valid derived field', async () => {
      const formData: CreateFormRequest = {
        name: 'Test Form',
        fields: [
          {
            id: 'field1',
            type: 'number',
            label: 'Base Value',
            required: true,
          },
          {
            id: 'field2',
            type: 'number',
            label: 'Multiplied Value',
            required: false,
            derived: {
              isDerived: true,
              parents: ['field1'],
              formula: '$field1 * 2',
            },
          },
        ],
      };

      const result = await formService.createForm(formData);

      expect(result.success).toBe(true);
    });
  });

  describe('getFormById', () => {
    it('should return form when it exists', async () => {
      // First create a form
      const formData: CreateFormRequest = {
        name: 'Test Form',
        fields: [
          {
            id: 'field1',
            type: 'text',
            label: 'Name',
            required: true,
          },
        ],
      };

      const createResult = await formService.createForm(formData);
      expect(createResult.success).toBe(true);

      if (createResult.success) {
        const getResult = await formService.getFormById(createResult.data.id);
        
        expect(getResult.success).toBe(true);
        if (getResult.success) {
          expect(getResult.data?.name).toBe('Test Form');
          expect(getResult.data?.fields).toHaveLength(1);
        }
      }
    });

    it('should return null when form does not exist', async () => {
      const result = await formService.getFormById('non-existent-id');
      
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toBe(null);
      }
    });

    it('should reject invalid form ID', async () => {
      const result = await formService.getFormById('');
      
      expect(result.success).toBe(false);
    });
  });

  describe('getAllForms', () => {
    it('should return empty array when no forms exist', async () => {
      const result = await formService.getAllForms();
      
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toEqual([]);
      }
    });

    it('should return all forms', async () => {
      // Create multiple forms
      const formData1: CreateFormRequest = {
        name: 'Form 1',
        fields: [{ id: 'f1', type: 'text', label: 'Field 1', required: true }],
      };
      
      const formData2: CreateFormRequest = {
        name: 'Form 2',
        fields: [{ id: 'f2', type: 'number', label: 'Field 2', required: false }],
      };

      await formService.createForm(formData1);
      await formService.createForm(formData2);

      const result = await formService.getAllForms();
      
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toHaveLength(2);
        expect(result.data[0].name).toBe('Form 1');
        expect(result.data[1].name).toBe('Form 2');
      }
    });
  });
});
