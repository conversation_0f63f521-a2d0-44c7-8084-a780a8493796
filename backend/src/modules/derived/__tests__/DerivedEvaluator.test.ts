import { DerivedEvaluator } from '../DerivedEvaluator';

describe('DerivedEvaluator', () => {
  describe('basic arithmetic', () => {
    it('should evaluate simple addition', () => {
      const result = DerivedEvaluator.evaluate('2 + 3', {});
      expect(result).toBe(5);
    });

    it('should evaluate simple subtraction', () => {
      const result = DerivedEvaluator.evaluate('10 - 4', {});
      expect(result).toBe(6);
    });

    it('should evaluate simple multiplication', () => {
      const result = DerivedEvaluator.evaluate('3 * 4', {});
      expect(result).toBe(12);
    });

    it('should evaluate simple division', () => {
      const result = DerivedEvaluator.evaluate('15 / 3', {});
      expect(result).toBe(5);
    });

    it('should handle operator precedence', () => {
      const result = DerivedEvaluator.evaluate('2 + 3 * 4', {});
      expect(result).toBe(14);
    });

    it('should handle parentheses', () => {
      const result = DerivedEvaluator.evaluate('(2 + 3) * 4', {});
      expect(result).toBe(20);
    });

    it('should handle nested parentheses', () => {
      const result = DerivedEvaluator.evaluate('((2 + 3) * 4) - 5', {});
      expect(result).toBe(15);
    });

    it('should handle decimal numbers', () => {
      const result = DerivedEvaluator.evaluate('2.5 + 1.5', {});
      expect(result).toBe(4);
    });

    it('should handle unary minus', () => {
      const result = DerivedEvaluator.evaluate('-5 + 3', {});
      expect(result).toBe(-2);
    });

    it('should handle unary plus', () => {
      const result = DerivedEvaluator.evaluate('+5 + 3', {});
      expect(result).toBe(8);
    });
  });

  describe('field references', () => {
    it('should replace field references with values', () => {
      const fieldValues = { field1: 10, field2: 5 };
      const result = DerivedEvaluator.evaluate('$field1 + $field2', fieldValues);
      expect(result).toBe(15);
    });

    it('should handle complex expressions with field references', () => {
      const fieldValues = { a: 2, b: 3, c: 4 };
      const result = DerivedEvaluator.evaluate('$a + 2*($b - 1) + $c', fieldValues);
      expect(result).toBe(10); // 2 + 2*(3-1) + 4 = 2 + 4 + 4 = 10
    });

    it('should convert string numbers to numeric values', () => {
      const fieldValues = { field1: '10', field2: '5.5' };
      const result = DerivedEvaluator.evaluate('$field1 + $field2', fieldValues);
      expect(result).toBe(15.5);
    });

    it('should default missing fields to 0', () => {
      const fieldValues = { field1: 10 };
      const result = DerivedEvaluator.evaluate('$field1 + $field2', fieldValues);
      expect(result).toBe(10);
    });

    it('should default invalid field values to 0', () => {
      const fieldValues = { field1: 'invalid', field2: null, field3: undefined };
      const result = DerivedEvaluator.evaluate('$field1 + $field2 + $field3 + 5', fieldValues);
      expect(result).toBe(5);
    });
  });

  describe('error handling', () => {
    it('should return null for invalid expressions', () => {
      const result = DerivedEvaluator.evaluate('2 + + 3', {});
      expect(result).toBe(null);
    });

    it('should return null for expressions with invalid tokens', () => {
      const result = DerivedEvaluator.evaluate('alert("hack")', {});
      expect(result).toBe(null);
    });

    it('should return null for division by zero', () => {
      const result = DerivedEvaluator.evaluate('5 / 0', {});
      expect(result).toBe(null);
    });

    it('should return null for empty expressions', () => {
      const result = DerivedEvaluator.evaluate('', {});
      expect(result).toBe(null);
    });

    it('should return null for null expressions', () => {
      const result = DerivedEvaluator.evaluate(null as any, {});
      expect(result).toBe(null);
    });

    it('should return null for mismatched parentheses', () => {
      const result = DerivedEvaluator.evaluate('(2 + 3', {});
      expect(result).toBe(null);
    });

    it('should return null for expressions with extra characters', () => {
      const result = DerivedEvaluator.evaluate('2 + 3 abc', {});
      expect(result).toBe(null);
    });
  });

  describe('whitespace handling', () => {
    it('should handle expressions with various whitespace', () => {
      const result = DerivedEvaluator.evaluate('  2   +   3  ', {});
      expect(result).toBe(5);
    });

    it('should handle field references with whitespace', () => {
      const fieldValues = { field1: 10, field2: 5 };
      const result = DerivedEvaluator.evaluate(' $field1  +  $field2 ', fieldValues);
      expect(result).toBe(15);
    });
  });

  describe('real-world scenarios', () => {
    it('should calculate age from birth year', () => {
      const currentYear = new Date().getFullYear();
      const fieldValues = { birthYear: 1990, currentYear };
      const result = DerivedEvaluator.evaluate('$currentYear - $birthYear', fieldValues);
      expect(result).toBe(currentYear - 1990);
    });

    it('should calculate total price with tax', () => {
      const fieldValues = { price: 100, taxRate: 0.08 };
      const result = DerivedEvaluator.evaluate('$price * (1 + $taxRate)', fieldValues);
      expect(result).toBe(108);
    });

    it('should calculate BMI', () => {
      const fieldValues = { weight: 70, height: 1.75 };
      const result = DerivedEvaluator.evaluate('$weight / ($height * $height)', fieldValues);
      expect(result).toBeCloseTo(22.86, 2);
    });
  });
});
