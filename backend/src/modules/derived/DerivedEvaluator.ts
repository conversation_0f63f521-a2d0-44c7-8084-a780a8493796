/**
 * Safe expression evaluator for derived fields
 * Supports basic arithmetic operations and field references ($fieldId)
 */
export class DerivedEvaluator {
  private static readonly ALLOWED_TOKENS = /^[\d\s+\-*/().$a-zA-Z_]+$/;
  private static readonly FIELD_REFERENCE = /\$([a-zA-Z_][a-zA-Z0-9_]*)/g;

  /**
   * Evaluates a derived field expression with given field values
   * @param expression The formula expression (e.g., "$field1 + $field2 * 2")
   * @param fieldValues Map of field IDs to their numeric values
   * @returns The computed result or null if evaluation fails
   */
  static evaluate(expression: string, fieldValues: Record<string, unknown>): number | null {
    try {
      if (!expression || typeof expression !== 'string') {
        return null;
      }

      // Replace field references with their values
      const processedExpression = this.replaceFieldReferences(expression, fieldValues);
      
      // Validate that only allowed tokens are present
      if (!this.ALLOWED_TOKENS.test(processedExpression)) {
        console.warn('Invalid tokens in expression:', processedExpression);
        return null;
      }

      // Evaluate the expression safely
      return this.safeEvaluate(processedExpression);
    } catch (error) {
      console.warn('Error evaluating expression:', error);
      return null;
    }
  }

  /**
   * Replaces $fieldId references with their numeric values
   */
  private static replaceFieldReferences(
    expression: string, 
    fieldValues: Record<string, unknown>
  ): string {
    return expression.replace(this.FIELD_REFERENCE, (match, fieldId) => {
      const value = fieldValues[fieldId];
      const numericValue = this.toNumber(value);
      return numericValue.toString();
    });
  }

  /**
   * Converts a value to a number, defaulting to 0 for invalid values
   */
  private static toNumber(value: unknown): number {
    if (typeof value === 'number' && !isNaN(value)) {
      return value;
    }
    
    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? 0 : parsed;
    }
    
    return 0;
  }

  /**
   * Safely evaluates a mathematical expression using a simple parser
   * This avoids using eval() for security reasons
   */
  private static safeEvaluate(expression: string): number | null {
    try {
      // Remove whitespace
      const cleanExpression = expression.replace(/\s/g, '');
      
      // Parse and evaluate using recursive descent parser
      const parser = new ExpressionParser(cleanExpression);
      return parser.parse();
    } catch (error) {
      console.warn('Error in safe evaluation:', error);
      return null;
    }
  }
}

/**
 * Simple recursive descent parser for mathematical expressions
 * Supports: +, -, *, /, (), numbers
 */
class ExpressionParser {
  private pos = 0;
  
  constructor(private expression: string) {}

  parse(): number {
    const result = this.parseExpression();
    if (this.pos < this.expression.length) {
      throw new Error('Unexpected characters at end of expression');
    }
    return result;
  }

  private parseExpression(): number {
    let result = this.parseTerm();
    
    while (this.pos < this.expression.length) {
      const op = this.expression[this.pos];
      if (op === '+') {
        this.pos++;
        result += this.parseTerm();
      } else if (op === '-') {
        this.pos++;
        result -= this.parseTerm();
      } else {
        break;
      }
    }
    
    return result;
  }

  private parseTerm(): number {
    let result = this.parseFactor();
    
    while (this.pos < this.expression.length) {
      const op = this.expression[this.pos];
      if (op === '*') {
        this.pos++;
        result *= this.parseFactor();
      } else if (op === '/') {
        this.pos++;
        const divisor = this.parseFactor();
        if (divisor === 0) {
          throw new Error('Division by zero');
        }
        result /= divisor;
      } else {
        break;
      }
    }
    
    return result;
  }

  private parseFactor(): number {
    if (this.pos >= this.expression.length) {
      throw new Error('Unexpected end of expression');
    }

    const char = this.expression[this.pos];
    
    // Handle parentheses
    if (char === '(') {
      this.pos++;
      const result = this.parseExpression();
      if (this.pos >= this.expression.length || this.expression[this.pos] !== ')') {
        throw new Error('Missing closing parenthesis');
      }
      this.pos++;
      return result;
    }
    
    // Handle unary minus
    if (char === '-') {
      this.pos++;
      return -this.parseFactor();
    }
    
    // Handle unary plus
    if (char === '+') {
      this.pos++;
      return this.parseFactor();
    }
    
    // Handle numbers
    return this.parseNumber();
  }

  private parseNumber(): number {
    let numStr = '';
    let hasDecimal = false;
    
    while (this.pos < this.expression.length) {
      const char = this.expression[this.pos];
      
      if (char >= '0' && char <= '9') {
        numStr += char;
        this.pos++;
      } else if (char === '.' && !hasDecimal) {
        hasDecimal = true;
        numStr += char;
        this.pos++;
      } else {
        break;
      }
    }
    
    if (numStr === '' || numStr === '.') {
      throw new Error('Invalid number');
    }
    
    return parseFloat(numStr);
  }
}
