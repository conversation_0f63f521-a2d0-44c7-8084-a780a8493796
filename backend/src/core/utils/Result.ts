export type Result<T, E = Error> = 
  | { success: true; data: T }
  | { success: false; error: E };

export const Result = {
  success: <T>(data: T): Result<T> => ({ success: true, data }),
  failure: <E>(error: E): Result<never, E> => ({ success: false, error }),
};

export function isSuccess<T, E>(result: Result<T, E>): result is { success: true; data: T } {
  return result.success;
}

export function isFailure<T, E>(result: Result<T, E>): result is { success: false; error: E } {
  return !result.success;
}
