{"name": "@form-builder/backend", "version": "1.0.0", "description": "Backend API for form builder", "main": "dist/server.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "typecheck": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@form-builder/shared": "file:../shared", "@supabase/supabase-js": "^2.38.4", "cors": "^2.8.5", "express": "^4.18.2", "helmet": "^7.1.0", "pino": "^8.16.2", "pino-pretty": "^10.2.3", "zod": "^3.22.4"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "husky": "^8.0.3", "jest": "^29.7.0", "prettier": "^3.1.0", "supertest": "^6.3.3", "@types/supertest": "^2.0.16", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "typescript": "^5.2.2"}}