{"name": "form-builder-monorepo", "version": "1.0.0", "description": "Full-stack React + Node/Express form builder application", "private": true, "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:shared && npm run build:backend && npm run build:frontend", "build:shared": "cd shared && npm run build", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "test": "npm run test:backend", "test:backend": "cd backend && npm test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "typecheck": "npm run typecheck:backend && npm run typecheck:frontend", "typecheck:backend": "cd backend && npm run typecheck", "typecheck:frontend": "cd frontend && npm run typecheck"}, "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["backend", "frontend", "shared"]}