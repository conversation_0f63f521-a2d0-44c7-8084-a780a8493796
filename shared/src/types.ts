import { z } from 'zod';

// Field Types
export const FieldTypeSchema = z.enum([
  'text',
  'number',
  'textarea',
  'select',
  'radio',
  'checkbox',
  'date',
]);

export type FieldType = z.infer<typeof FieldTypeSchema>;

// Validation Rules
export const ValidationRuleSchema = z.object({
  notEmpty: z.boolean().optional(),
  minLength: z.number().min(0).optional(),
  maxLength: z.number().min(0).optional(),
  email: z.boolean().optional(),
  passwordRule: z.boolean().optional(), // ≥8 chars, ≥1 number
});

export type ValidationRule = z.infer<typeof ValidationRuleSchema>;

// Field Options (for select, radio, checkbox)
export const FieldOptionSchema = z.object({
  label: z.string(),
  value: z.string(),
});

export type FieldOption = z.infer<typeof FieldOptionSchema>;

// Derived Field Configuration
export const DerivedConfigSchema = z.object({
  isDerived: z.boolean(),
  parents: z.array(z.string()), // field ids
  formula: z.string().optional(), // expression like "$fieldId1 + $fieldId2"
});

export type DerivedConfig = z.infer<typeof DerivedConfigSchema>;

// Form Field
export const FormFieldSchema = z.object({
  id: z.string(),
  type: FieldTypeSchema,
  label: z.string(),
  required: z.boolean(),
  defaultValue: z.unknown().optional(),
  options: z.array(FieldOptionSchema).optional(), // for select/radio/checkbox
  validations: ValidationRuleSchema.optional(),
  derived: DerivedConfigSchema.optional(),
});

export type FormField = z.infer<typeof FormFieldSchema>;

// Form Schema
export const FormSchemaSchema = z.object({
  id: z.string().optional(),
  name: z.string(),
  fields: z.array(FormFieldSchema),
  createdAt: z.string().optional(),
});

export type FormSchema = z.infer<typeof FormSchemaSchema>;

// DTOs for API
export const CreateFormRequestSchema = z.object({
  name: z.string().min(1, 'Form name is required'),
  fields: z.array(FormFieldSchema),
});

export type CreateFormRequest = z.infer<typeof CreateFormRequestSchema>;

export const CreateFormResponseSchema = z.object({
  id: z.string(),
  name: z.string(),
  createdAt: z.string(),
});

export type CreateFormResponse = z.infer<typeof CreateFormResponseSchema>;

export const GetFormResponseSchema = FormSchemaSchema;
export type GetFormResponse = z.infer<typeof GetFormResponseSchema>;

export const ListFormsResponseSchema = z.array(
  z.object({
    id: z.string(),
    name: z.string(),
    createdAt: z.string(),
  })
);

export type ListFormsResponse = z.infer<typeof ListFormsResponseSchema>;

// Error Response
export const ErrorResponseSchema = z.object({
  error: z.object({
    code: z.string(),
    message: z.string(),
    details: z.unknown().optional(),
  }),
});

export type ErrorResponse = z.infer<typeof ErrorResponseSchema>;

// Result type for functional error handling
export type Result<T, E = Error> = 
  | { success: true; data: T }
  | { success: false; error: E };
