import { z } from 'zod';
import { ValidationRule } from './types';

/**
 * Validates a field value against the provided validation rules
 */
export function validateFieldValue(
  value: unknown,
  validations?: ValidationRule,
  fieldType?: string
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!validations) {
    return { isValid: true, errors: [] };
  }

  const stringValue = String(value || '');

  // Not empty validation
  if (validations.notEmpty && (!value || stringValue.trim() === '')) {
    errors.push('This field is required');
  }

  // Skip other validations if value is empty and not required
  if (!stringValue.trim() && !validations.notEmpty) {
    return { isValid: true, errors: [] };
  }

  // Min length validation
  if (validations.minLength !== undefined && stringValue.length < validations.minLength) {
    errors.push(`Minimum length is ${validations.minLength} characters`);
  }

  // Max length validation
  if (validations.maxLength !== undefined && stringValue.length > validations.maxLength) {
    errors.push(`Maximum length is ${validations.maxLength} characters`);
  }

  // Email validation
  if (validations.email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(stringValue)) {
      errors.push('Please enter a valid email address');
    }
  }

  // Password rule validation (≥8 chars, ≥1 number)
  if (validations.passwordRule) {
    if (stringValue.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    if (!/\d/.test(stringValue)) {
      errors.push('Password must contain at least one number');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Validates form data against form schema
 */
export function validateFormData(
  formData: Record<string, unknown>,
  formFields: Array<{ id: string; required: boolean; validations?: ValidationRule; type: string }>
): { isValid: boolean; fieldErrors: Record<string, string[]> } {
  const fieldErrors: Record<string, string[]> = {};

  for (const field of formFields) {
    const value = formData[field.id];
    
    // Check required fields
    if (field.required && (!value || String(value).trim() === '')) {
      fieldErrors[field.id] = ['This field is required'];
      continue;
    }

    // Validate field value
    const validation = validateFieldValue(value, field.validations, field.type);
    if (!validation.isValid) {
      fieldErrors[field.id] = validation.errors;
    }
  }

  return {
    isValid: Object.keys(fieldErrors).length === 0,
    fieldErrors,
  };
}
